{"id": "26b0f567-1325-4d20-a271-40fa4840866b", "prevId": "12c01bb7-da64-45ce-8e0d-67996482ffc6", "version": "7", "dialect": "postgresql", "tables": {"public.agent_versions": {"name": "agent_versions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "flow_definition": {"name": "flow_definition", "type": "json", "primaryKey": false, "notNull": true}, "changelog": {"name": "changelog", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"agent_versions_agent_id_agents_id_fk": {"name": "agent_versions_agent_id_agents_id_fk", "tableFrom": "agent_versions", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "agent_versions_created_by_users_id_fk": {"name": "agent_versions_created_by_users_id_fk", "tableFrom": "agent_versions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "agent_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "flow_definition": {"name": "flow_definition", "type": "json", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"agents_workspace_id_workspaces_id_fk": {"name": "agents_workspace_id_workspaces_id_fk", "tableFrom": "agents", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "agents_created_by_users_id_fk": {"name": "agents_created_by_users_id_fk", "tableFrom": "agents", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.execution_logs": {"name": "execution_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "execution_id": {"name": "execution_id", "type": "integer", "primaryKey": false, "notNull": true}, "node_id": {"name": "node_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "step_number": {"name": "step_number", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "execution_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "input": {"name": "input", "type": "json", "primaryKey": false, "notNull": false}, "output": {"name": "output", "type": "json", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "tokens_used": {"name": "tokens_used", "type": "integer", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric(10, 6)", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"execution_logs_execution_id_executions_id_fk": {"name": "execution_logs_execution_id_executions_id_fk", "tableFrom": "execution_logs", "tableTo": "executions", "columnsFrom": ["execution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.executions": {"name": "executions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "trigger_type": {"name": "trigger_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "trigger_data": {"name": "trigger_data", "type": "json", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "execution_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"executions_agent_id_agents_id_fk": {"name": "executions_agent_id_agents_id_fk", "tableFrom": "executions", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.flow_edges": {"name": "flow_edges", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "edge_id": {"name": "edge_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "target": {"name": "target", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "source_handle": {"name": "source_handle", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "target_handle": {"name": "target_handle", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"flow_edges_agent_id_agents_id_fk": {"name": "flow_edges_agent_id_agents_id_fk", "tableFrom": "flow_edges", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.flow_nodes": {"name": "flow_nodes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "node_id": {"name": "node_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "node_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "json", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"flow_nodes_agent_id_agents_id_fk": {"name": "flow_nodes_agent_id_agents_id_fk", "tableFrom": "flow_nodes", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspace_members": {"name": "workspace_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "workspace_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "invited_at": {"name": "invited_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"workspace_members_workspace_id_workspaces_id_fk": {"name": "workspace_members_workspace_id_workspaces_id_fk", "tableFrom": "workspace_members", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workspace_members_user_id_users_id_fk": {"name": "workspace_members_user_id_users_id_fk", "tableFrom": "workspace_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workspaces_owner_id_users_id_fk": {"name": "workspaces_owner_id_users_id_fk", "tableFrom": "workspaces", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_slug_unique": {"name": "workspaces_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integration_credentials": {"name": "integration_credentials", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "integration_id": {"name": "integration_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "encrypted_tokens": {"name": "encrypted_tokens", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"integration_credentials_integration_id_integrations_id_fk": {"name": "integration_credentials_integration_id_integrations_id_fk", "tableFrom": "integration_credentials", "tableTo": "integrations", "columnsFrom": ["integration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "integration_credentials_user_id_users_id_fk": {"name": "integration_credentials_user_id_users_id_fk", "tableFrom": "integration_credentials", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integrations": {"name": "integrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "integration_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"integrations_workspace_id_workspaces_id_fk": {"name": "integrations_workspace_id_workspaces_id_fk", "tableFrom": "integrations", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "integrations_created_by_users_id_fk": {"name": "integrations_created_by_users_id_fk", "tableFrom": "integrations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.agent_status": {"name": "agent_status", "schema": "public", "values": ["draft", "active", "paused", "archived"]}, "public.execution_status": {"name": "execution_status", "schema": "public", "values": ["pending", "running", "completed", "failed", "cancelled"]}, "public.node_type": {"name": "node_type", "schema": "public", "values": ["trigger_gmail", "trigger_slack", "trigger_webhook", "trigger_scheduler", "prompt_llm", "prompt_memory", "action_slack", "action_notion", "action_email", "action_webhook", "condition", "transformer"]}, "public.workspace_role": {"name": "workspace_role", "schema": "public", "values": ["owner", "admin", "editor", "viewer"]}, "public.integration_type": {"name": "integration_type", "schema": "public", "values": ["gmail", "slack", "notion", "webhook", "openai", "pinecone", "langfuse"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}