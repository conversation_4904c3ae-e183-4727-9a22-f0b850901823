{"name": "database", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "migrate": "npx drizzle-kit migrate", "generate": "drizzle-kit generate", "push": "drizzle-kit push"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "pg": "^8.16.0"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "^22.15.18", "@types/pg": "^8.15.2", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4"}}