{"name": "server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "dev": "nest start --watch", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/bull": "^10.0.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@repo/types": "workspace:*", "bcryptjs": "^3.0.2", "bull": "^4.12.2", "bullmq": "^5.53.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "database": "workspace:*", "drizzle-orm": "^0.43.1", "pg": "^8.16.0", "ioredis": "^5.3.2", "langchain": "^0.1.25", "@slack/web-api": "^7.0.2", "googleapis": "^131.0.0", "axios": "^1.6.7", "openai": "^4.28.0", "node-cron": "^3.0.3", "@notionhq/client": "^2.2.15", "crypto": "^1.0.1", "moment-timezone": "^0.5.45", "@pinecone-database/pinecone": "^2.0.1", "@google-cloud/pubsub": "^4.1.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bull": "^4.10.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/pg": "^8.15.2", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}