import { Module } from '@nestjs/common';
import { IntegrationService } from './integration.service';
import { IntegrationsController } from './integrations.controller';
import { DeepSeekService } from './services/deepseek.service';
import { SlackService } from './services/slack.service';
import { GmailService } from './services/gmail.service';

@Module({
  controllers: [IntegrationsController],
  providers: [
    IntegrationService,
    DeepSeekService,
    SlackService,
    GmailService,
  ],
  exports: [
    IntegrationService,
    DeepSeekService,
    SlackService,
    GmailService,
  ],
})
export class IntegrationsModule {}
