import { Modu<PERSON> } from '@nestjs/common';
import { IntegrationService } from './integration.service';
import { IntegrationsController } from './integrations.controller';
import { DeepSeekService } from './services/deepseek.service';
import { SlackService } from './services/slack.service';
import { GmailService } from './services/gmail.service';
import { NotionService } from './services/notion.service';
import { PineconeService } from './services/pinecone.service';

@Module({
  controllers: [IntegrationsController],
  providers: [
    IntegrationService,
    DeepSeekService,
    SlackService,
    GmailService,
    NotionService,
    PineconeService,
  ],
  exports: [
    IntegrationService,
    DeepSeekService,
    SlackService,
    GmailService,
    NotionService,
    PineconeService,
  ],
})
export class IntegrationsModule {}
